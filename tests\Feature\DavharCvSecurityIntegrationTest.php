<?php

namespace Tests\Feature;

use App\Models\Sukh;
use App\Models\Bair;
use App\Models\Korpus;
use App\Models\Orc;
use App\Models\Davhar;
use App\Services\DavharSyncService;
use App\Services\CvSecurityService\CvSecurityServiceExt;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;
use Mockery;

class DavharCvSecurityIntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected $davharSyncService;
    protected $cvSecurityServiceMock;

    protected function setUp(): void
    {
        parent::setUp();

        // Mock the CVSecurity service
        $this->cvSecurityServiceMock = Mockery::mock(CvSecurityServiceExt::class);
        $this->app->instance(CvSecurityServiceExt::class, $this->cvSecurityServiceMock);

        // Create DavharSyncService with mocked dependency
        $this->davharSyncService = new DavharSyncService($this->cvSecurityServiceMock);
        $this->app->instance(DavharSyncService::class, $this->davharSyncService);
    }

    /** @test */
    public function it_creates_davhar_with_cv_security_sync()
    {
        // Arrange
        $sukh = Sukh::factory()->create();
        $bair = Bair::factory()->create(['sukh_id' => $sukh->id]);
        $korpus = Korpus::factory()->create(['bair_id' => $bair->id]);
        $orc = Orc::factory()->create([
            'korpus_id' => $korpus->id,
            'number' => '1',
            'code' => 'ENTR_000001'
        ]);

        // Mock CVSecurity service responses
        $this->cvSecurityServiceMock
            ->shouldReceive('isServiceAvailable')
            ->andReturn(true);

        $this->cvSecurityServiceMock
            ->shouldReceive('createEleLevel')
            ->with([
                'name' => 'Floor 4',
                'remark' => 'Floor 4 in Entrance 1',
                'parent_code' => 'ENTR_000001',
            ])
            ->andReturn(['code' => 'ELEV_000001']);

        // Act
        $davhar = Davhar::create([
            'orc_id' => $orc->id,
            'number' => '4',
            'order' => 1,
            'begin_toot_number' => 1,
            'end_toot_number' => 6,
        ]);

        // Manually trigger the sync since observer might not be working in tests
        $this->davharSyncService->syncCreate($davhar);

        // Refresh the model to get updated data
        $davhar->refresh();

        // Assert
        $this->assertDatabaseHas('davhars', [
            'id' => $davhar->id,
            'orc_id' => $orc->id,
            'number' => '4',
            'order' => 1,
            'begin_toot_number' => 1,
            'end_toot_number' => 6,
            'code' => 'ELEV_000001',
        ]);
    }

    /** @test */
    public function it_updates_davhar_with_cv_security_sync()
    {
        // Arrange
        $sukh = Sukh::factory()->create();
        $bair = Bair::factory()->create(['sukh_id' => $sukh->id]);
        $korpus = Korpus::factory()->create(['bair_id' => $bair->id]);
        $orc = Orc::factory()->create([
            'korpus_id' => $korpus->id,
            'number' => '1',
            'code' => 'ENTR_000001'
        ]);

        $davhar = Davhar::create([
            'orc_id' => $orc->id,
            'number' => '4',
            'order' => 1,
            'begin_toot_number' => 1,
            'end_toot_number' => 6,
            'code' => 'ELEV_000001',
        ]);

        // Mock CVSecurity service responses
        $this->cvSecurityServiceMock
            ->shouldReceive('isServiceAvailable')
            ->once()
            ->andReturn(true);

        $this->cvSecurityServiceMock
            ->shouldReceive('updateEleLevel')
            ->once()
            ->with('ELEV_000001', [
                'name' => 'Floor 5',
                'remark' => 'Floor 5 in Entrance 1',
                'parent_code' => 'ENTR_000001',
            ])
            ->andReturn(['code' => 'ELEV_000001']);

        // Act
        $davhar->update(['number' => '5']);

        // Assert
        $this->assertDatabaseHas('davhars', [
            'id' => $davhar->id,
            'number' => '5',
            'code' => 'ELEV_000001',
        ]);
    }

    /** @test */
    public function it_deletes_davhar_with_cv_security_sync()
    {
        // Arrange
        $sukh = Sukh::factory()->create();
        $bair = Bair::factory()->create(['sukh_id' => $sukh->id]);
        $korpus = Korpus::factory()->create(['bair_id' => $bair->id]);
        $orc = Orc::factory()->create([
            'korpus_id' => $korpus->id,
            'number' => '1',
            'code' => 'ENTR_000001'
        ]);

        $davhar = Davhar::create([
            'orc_id' => $orc->id,
            'number' => '4',
            'order' => 1,
            'begin_toot_number' => 1,
            'end_toot_number' => 6,
            'code' => 'ELEV_000001',
        ]);

        // Mock CVSecurity service responses
        $this->cvSecurityServiceMock
            ->shouldReceive('isServiceAvailable')
            ->once()
            ->andReturn(true);

        $this->cvSecurityServiceMock
            ->shouldReceive('deleteEleLevel')
            ->once()
            ->with('ELEV_000001')
            ->andReturn(true);

        // Act
        $davhar->delete();

        // Assert
        $this->assertDatabaseMissing('davhars', [
            'id' => $davhar->id,
        ]);
    }

    /** @test */
    public function it_handles_cv_security_service_unavailable()
    {
        // Arrange
        $sukh = Sukh::factory()->create();
        $bair = Bair::factory()->create(['sukh_id' => $sukh->id]);
        $korpus = Korpus::factory()->create(['bair_id' => $bair->id]);
        $orc = Orc::factory()->create([
            'korpus_id' => $korpus->id,
            'number' => '1',
            'code' => 'ENTR_000001'
        ]);

        // Mock CVSecurity service as unavailable
        $this->cvSecurityServiceMock
            ->shouldReceive('isServiceAvailable')
            ->once()
            ->andReturn(false);

        // Act
        $davhar = Davhar::create([
            'orc_id' => $orc->id,
            'number' => '4',
            'order' => 1,
            'begin_toot_number' => 1,
            'end_toot_number' => 6,
        ]);

        // Assert
        $this->assertDatabaseHas('davhars', [
            'id' => $davhar->id,
            'orc_id' => $orc->id,
            'number' => '4',
            'code' => null, // Should be null when service is unavailable
        ]);
    }

    /** @test */
    public function it_establishes_correct_relationships()
    {
        // Arrange
        $sukh = Sukh::factory()->create();
        $bair = Bair::factory()->create(['sukh_id' => $sukh->id]);
        $korpus = Korpus::factory()->create(['bair_id' => $bair->id]);
        $orc = Orc::factory()->create(['korpus_id' => $korpus->id]);

        $davhar = Davhar::create([
            'orc_id' => $orc->id,
            'number' => '4',
        ]);

        // Act & Assert
        $this->assertEquals($orc->id, $davhar->orc->id);
        $this->assertTrue($orc->davhars->contains($davhar));
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
