<?php

use App\Models\Bair;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bairs', function (Blueprint $table) {
            $table->id();
            $table->string(Bair::NAME);
            $table->foreignId(Bair::SUKH_ID)->nullable();
            $table->foreignId(Bair::AIMAG_ID)->nullable();
            $table->foreignId(Bair::SOUM_ID)->nullable();
            $table->foreignId(Bair::BAG_ID)->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bairs');
    }
};
