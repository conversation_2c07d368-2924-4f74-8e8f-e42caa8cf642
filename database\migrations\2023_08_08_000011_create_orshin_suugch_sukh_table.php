<?php

use App\Models\OrshinSuugchToot;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orshin_suugch_sukh', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('orshin_suugch_id');
            $table->unsignedBigInteger('sukh_id');
            $table->foreign('orshin_suugch_id')->references('id')->on('orshin_suugches')->onDelete('cascade');
            $table->foreign('sukh_id')->references('id')->on('sukhs')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orshin_suugch_sukh');
    }
};
