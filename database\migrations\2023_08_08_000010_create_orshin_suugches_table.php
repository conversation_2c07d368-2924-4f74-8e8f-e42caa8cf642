<?php

use App\Models\OrshinSuugch;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orshin_suugches', function (Blueprint $table) {
            $table->id();
            $table->string(OrshinSuugch::LAST_NAME)->nullable();
            $table->string(OrshinSuugch::NAME)->nullable(); // Made nullable
            $table->string(OrshinSuugch::PHONE)->unique();
            $table->string(OrshinSuugch::EMAIL)->unique()->nullable();
            $table->boolean(OrshinSuugch::IS_ADMIN)->default(false);
            $table->unsignedBigInteger(OrshinSuugch::PARENT_ID)->nullable();
            $table->string('device_code')->nullable();
            $table->string('uniq_code')->unique()->nullable(); // Changed from unsignedBigInteger to string
            $table->string(OrshinSuugch::CODE, 30)->nullable(); // CVSecurity integration code
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orshin_suugches');
    }
};
