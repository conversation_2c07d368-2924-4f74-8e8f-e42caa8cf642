<?php

namespace App\Filament\Resources\Admin;

use App\Filament\Resources\Admin\OrcResource\Pages;
use App\Filament\Resources\Admin\OrcResource\RelationManagers;
use App\Models\Orc;
use App\Models\Korpus;
use App\Services\UserInfoService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Validation\Rules\Unique;

class OrcResource extends Resource
{
    protected static ?string $model = Orc::class;

    protected static ?string $navigationIcon = 'heroicon-o-arrow-right-on-rectangle';
    protected static ?string $navigationLabel = 'Орцууд';
    protected static ?string $pluralModelLabel = 'Орцууд';
    protected static ?string $modelLabel = 'орц';
    protected static ?string $slug = 'orcs';

    // Hide from navigation - only accessible through Korpus hierarchy
    protected static bool $shouldRegisterNavigation = false;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Placeholder::make('korpus_info')
                            ->label('Блок')
                            ->content(function (?Orc $record) {
                                if ($record && $record->korpus) {
                                    return "{$record->korpus->bair->name} - {$record->korpus->name}";
                                }

                                $korpusId = request()->get('korpus_id');
                                if ($korpusId) {
                                    $korpus = \App\Models\Korpus::with('bair')->find($korpusId);
                                    return $korpus ? "{$korpus->bair->name} - {$korpus->name}" : 'Тодорхойгүй';
                                }

                                return 'Тодорхойгүй';
                            }),

                        Forms\Components\Hidden::make(Orc::KORPUS_ID)
                            ->default(function () {
                                return request()->get('korpus_id');
                            }),

                        Forms\Components\TextInput::make(Orc::NUMBER)
                            ->label('Орцны дугаар')
                            ->unique(
                                ignoreRecord: true,
                                modifyRuleUsing: function (Unique $rule, callable $get) {
                                    $korpusId = $get('korpus_id');
                                    return $rule->where('korpus_id', $korpusId);
                                }
                            )
                            ->numeric()
                            ->required(),

                        Forms\Components\TextInput::make(Orc::CODE)
                            ->label('CV Security код')
                            ->disabled()
                            ->dehydrated(false)
                            ->helperText('Энэ талбар автоматаар CV Security системээс ирнэ'),
                    ])
                    ->columns(2)
                    ->columnSpan(['lg' => fn (?Orc $record) => $record === null ? 3 : 2]),

                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Placeholder::make('created_at')
                            ->label('Үүсгэсэн огноо')
                            ->content(fn (Orc $record): ?string => $record->created_at?->diffForHumans()),

                        Forms\Components\Placeholder::make('updated_at')
                            ->label('Шинэчилсэн огноо')
                            ->content(fn (Orc $record): ?string => $record->updated_at?->diffForHumans()),
                    ])
                    ->columnSpan(['lg' => 1])
                    ->hidden(fn (?Orc $record) => $record === null),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->heading('Орцууд')
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make(Orc::NUMBER)->label('Орцны дугаар')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('korpus.name')->label('Блок')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('korpus.bair.name')->label('Байр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make(Orc::CODE)->label('CV Security код')->sortable(),
                Tables\Columns\TextColumn::make('davhars_count')->counts('davhars')->label('Давхарын тоо'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->emptyStateActions([
                Tables\Actions\CreateAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\DavharsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListOrcs::route('/'),
            'create' => Pages\CreateOrc::route('/create'),
            'edit' => Pages\EditOrc::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        $service = resolve(UserInfoService::class);
        $sukh = $service->getAUSukh();
        return parent::getEloquentQuery()->whereHas('korpus.bair.sukh', function (Builder $query) use($sukh) {
            $query->where('id', $sukh->id);
        });
    }
}
