<?php

namespace App\Filament\Resources\Admin\BairResource\RelationManagers;

use App\Models\Korpus;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class KorpusesRelationManager extends RelationManager
{
    protected static string $relationship = 'korpuses';

    public function form(Form $form): Form
    {
        // No modal form needed - using page-based navigation
        return $form->schema([]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('number')
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make(Korpus::NAME)->label('Нэр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make(Korpus::ORDER)->label('Дараалал')->sortable()->searchable(),
                Tables\Columns\TextColumn::make(Korpus::CODE)->label('CV Security код')->sortable(),
                Tables\Columns\TextColumn::make('orcs_count')->counts('orcs')->label('Орцны тоо'),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\Action::make('create')
                    ->label('Нэмэх')
                    ->icon('heroicon-s-plus')
                    ->url(fn (): string => route('filament.admin.resources.korpuses.create', ['bair_id' => $this->getOwnerRecord()->id])),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->url(fn (Korpus $record): string => route('filament.admin.resources.korpuses.edit', $record)),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
