<?php

namespace App\Filament\Resources\Admin\KorpusResource\Pages;

use App\Filament\Resources\Admin\KorpusResource;
use App\Models\Korpus;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditKorpus extends EditRecord
{
    protected static string $resource = KorpusResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    public function getBreadcrumbs(): array
    {
        $record = $this->getRecord();
        return [
            url()->route('filament.admin.resources.bairs.index') => 'Байрууд',
            url()->route('filament.admin.resources.bairs.edit', $record->bair_id) => $record->bair->name,
            '#' => "Блок: {$record->name}",
        ];
    }

    protected function getRedirectUrl(): string
    {
        $record = $this->getRecord();
        return url()->route('filament.admin.resources.bairs.edit', $record->bair_id);
    }
}
