<?php

namespace App\Filament\Resources\Admin;

use App\Filament\Resources\Admin\KorpusResource\Pages;
use App\Filament\Resources\Admin\KorpusResource\RelationManagers;
use App\Models\Korpus;
use App\Models\Bair;
use App\Services\UserInfoService;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Validation\Rules\Unique;

class KorpusResource extends Resource
{
    protected static ?string $model = Korpus::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-office';
    protected static ?string $navigationLabel = 'Блокууд';
    protected static ?string $pluralModelLabel = 'Блокууд';
    protected static ?string $modelLabel = 'блок';
    protected static ?string $slug = 'korpuses';

    // Hide from navigation - only accessible through Bair hierarchy
    protected static bool $shouldRegisterNavigation = false;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Placeholder::make('bair_info')
                            ->label('Байр')
                            ->content(function (?Korpus $record) {
                                if ($record && $record->bair) {
                                    return $record->bair->name;
                                }

                                $bairId = request()->get('bair_id');
                                if ($bairId) {
                                    $bair = \App\Models\Bair::find($bairId);
                                    return $bair ? $bair->name : 'Тодорхойгүй';
                                }

                                return 'Тодорхойгүй';
                            }),

                        Forms\Components\Hidden::make(Korpus::BAIR_ID)
                            ->default(function () {
                                return request()->get('bair_id');
                            }),

                        Forms\Components\TextInput::make(Korpus::NAME)
                            ->label('Нэр')
                            ->unique(
                                ignoreRecord: true,
                                modifyRuleUsing: function (Unique $rule, callable $get) {
                                    $bairId = $get('bair_id');
                                    return $rule->where('bair_id', $bairId);
                                }
                            )
                            ->required(),



                        Forms\Components\TextInput::make(Korpus::CODE)
                            ->label('CV Security код')
                            ->disabled()
                            ->dehydrated(false)
                            ->helperText('Энэ талбар автоматаар CV Security системээс ирнэ'),
                    ])
                    ->columns(2)
                    ->columnSpan(['lg' => fn (?Korpus $record) => $record === null ? 3 : 2]),

                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Placeholder::make('code')
                            ->label('CV Security код')
                            ->content(fn (Korpus $record): ?string => $record->code ?? 'Тодорхойгүй'),
                    ])
                    ->columnSpan(['lg' => 1])
                    ->hidden(fn (?Korpus $record) => $record === null),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->heading('Блокууд')
            ->columns([
                Tables\Columns\TextColumn::make('')->label('№')->rowIndex()->sortable(),
                Tables\Columns\TextColumn::make(Korpus::NAME)->label('Нэр')->sortable()->searchable(),
                Tables\Columns\TextColumn::make('bair.name')->label('Байр')->sortable()->searchable(),

                Tables\Columns\TextColumn::make(Korpus::CODE)->label('CV Security код')->sortable(),
                Tables\Columns\TextColumn::make('orcs_count')->counts('orcs')->label('Орцны тоо'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->emptyStateActions([
                Tables\Actions\CreateAction::make(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            RelationManagers\OrcsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListKorpuses::route('/'),
            'create' => Pages\CreateKorpus::route('/create'),
            'edit' => Pages\EditKorpus::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        $service = resolve(UserInfoService::class);
        $sukh = $service->getAUSukh();
        return parent::getEloquentQuery()->whereHas('bair.sukh', function (Builder $query) use($sukh) {
            $query->where('id', $sukh->id);
        });
    }
}
