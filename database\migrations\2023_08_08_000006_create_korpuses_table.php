<?php

use App\Models\Korpus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('korpuses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('bair_id')->constrained('bairs')->cascadeOnDelete();
            $table->string('name');
            $table->string(Korpus::CODE, 30)->nullable(); // CVSecurity integration code
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('korpuses');
    }
};
