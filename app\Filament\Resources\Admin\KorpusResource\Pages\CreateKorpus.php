<?php

namespace App\Filament\Resources\Admin\KorpusResource\Pages;

use App\Filament\Resources\Admin\KorpusResource;
use App\Models\Korpus;
use App\Models\Orc;
use Filament\Resources\Pages\CreateRecord;

class CreateKorpus extends CreateRecord
{
    protected static string $resource = KorpusResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // If bair_id is passed via URL parameter, set it
        if (request()->has('bair_id')) {
            $data[Korpus::BAIR_ID] = request()->get('bair_id');
        }
        
        return parent::mutateFormDataBeforeCreate($data);
    }

    protected function afterCreate(): void
    {
        $record = $this->record;
        // Create default Orc when Korpus is created
        $orc = new Orc([
            Orc::KORPUS_ID => $record->id, 
            Orc::NUMBER => 1
        ]);
        $record->orcs()->save($orc);
    }

    public function getBreadcrumbs(): array
    {
        $bairId = request()->get('bair_id');
        $breadcrumbs = [
            url()->route('filament.admin.resources.bairs.index') => 'Байрууд',
        ];
        
        if ($bairId) {
            $breadcrumbs[url()->route('filament.admin.resources.bairs.edit', $bairId)] = 'Байр засах';
        }
        
        $breadcrumbs['#'] = 'Блок нэмэх';
        
        return $breadcrumbs;
    }

    protected function getRedirectUrl(): string
    {
        $bairId = request()->get('bair_id');
        if ($bairId) {
            return url()->route('filament.admin.resources.bairs.edit', $bairId);
        }
        
        return $this->getResource()::getUrl('edit', ['record' => $this->getRecord()]);
    }
}
