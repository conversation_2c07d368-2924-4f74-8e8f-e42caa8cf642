<?php

namespace Tests\Feature;

use App\Models\Sukh;
use App\Models\Bair;
use App\Models\Korpus;
use App\Models\Orc;
use App\Models\Davhar;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class DavharBasicTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_creates_davhar_successfully()
    {
        // Arrange
        $sukh = Sukh::factory()->create();
        $bair = Bair::factory()->create(['sukh_id' => $sukh->id]);
        $korpus = Korpus::factory()->create(['bair_id' => $bair->id]);
        $orc = Orc::factory()->create(['korpus_id' => $korpus->id]);

        // Act
        $davhar = Davhar::create([
            'orc_id' => $orc->id,
            'number' => '4',
        ]);

        // Assert
        $this->assertDatabaseHas('davhars', [
            'id' => $davhar->id,
            'orc_id' => $orc->id,
            'number' => '4',
            'order' => 1,
            'begin_toot_number' => 1,
            'end_toot_number' => 6,
        ]);
    }

    /** @test */
    public function it_establishes_correct_relationships()
    {
        // Arrange
        $sukh = Sukh::factory()->create();
        $bair = Bair::factory()->create(['sukh_id' => $sukh->id]);
        $korpus = Korpus::factory()->create(['bair_id' => $bair->id]);
        $orc = Orc::factory()->create(['korpus_id' => $korpus->id]);

        $davhar = Davhar::create([
            'orc_id' => $orc->id,
            'number' => '4',
            'order' => 1,
            'begin_toot_number' => 1,
            'end_toot_number' => 6,
        ]);

        // Act & Assert
        $this->assertEquals($orc->id, $davhar->orc->id);
        $this->assertTrue($orc->davhars->contains($davhar));
        $this->assertEquals('4', $davhar->number);
        $this->assertEquals(1, $davhar->order);
    }

    /** @test */
    public function it_enforces_unique_order_within_orc()
    {
        // Arrange
        $sukh = Sukh::factory()->create();
        $bair = Bair::factory()->create(['sukh_id' => $sukh->id]);
        $korpus = Korpus::factory()->create(['bair_id' => $bair->id]);
        $orc = Orc::factory()->create(['korpus_id' => $korpus->id]);

        // Create first davhar
        Davhar::create([
            'orc_id' => $orc->id,
            'number' => '4',
            'order' => 1,
            'begin_toot_number' => 1,
            'end_toot_number' => 6,
        ]);

        // Act & Assert - Try to create another davhar with same order
        $this->expectException(\Illuminate\Database\QueryException::class);

        Davhar::create([
            'orc_id' => $orc->id,
            'number' => '5',
            'order' => 1, // Same order should fail
            'begin_toot_number' => 7,
            'end_toot_number' => 12,
        ]);
    }

    /** @test */
    public function it_allows_same_order_in_different_orcs()
    {
        // Arrange
        $sukh = Sukh::factory()->create();
        $bair = Bair::factory()->create(['sukh_id' => $sukh->id]);
        $korpus = Korpus::factory()->create(['bair_id' => $bair->id]);
        $orc1 = Orc::factory()->create(['korpus_id' => $korpus->id, 'number' => '1']);
        $orc2 = Orc::factory()->create(['korpus_id' => $korpus->id, 'number' => '2']);

        // Act - Create davhars with same order in different orcs
        $davhar1 = Davhar::create([
            'orc_id' => $orc1->id,
            'number' => '4',
            'order' => 1,
            'begin_toot_number' => 1,
            'end_toot_number' => 6,
        ]);

        $davhar2 = Davhar::create([
            'orc_id' => $orc2->id,
            'number' => '4',
            'order' => 1, // Same order but different orc should work
            'begin_toot_number' => 1,
            'end_toot_number' => 6,
        ]);

        // Assert
        $this->assertDatabaseHas('davhars', ['id' => $davhar1->id, 'order' => 1]);
        $this->assertDatabaseHas('davhars', ['id' => $davhar2->id, 'order' => 1]);
        $this->assertNotEquals($davhar1->orc_id, $davhar2->orc_id);
    }
}
