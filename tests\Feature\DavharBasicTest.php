<?php

namespace Tests\Feature;

use App\Models\Sukh;
use App\Models\Bair;
use App\Models\Korpus;
use App\Models\Orc;
use App\Models\Davhar;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class DavharBasicTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_creates_davhar_successfully()
    {
        // Arrange
        $sukh = Sukh::factory()->create();
        $bair = Bair::factory()->create(['sukh_id' => $sukh->id]);
        $korpus = Korpus::factory()->create(['bair_id' => $bair->id]);
        $orc = Orc::factory()->create(['korpus_id' => $korpus->id]);

        // Act
        $davhar = Davhar::create([
            'orc_id' => $orc->id,
            'number' => '4',
            'order' => 1,
            'begin_toot_number' => 1,
            'end_toot_number' => 6,
        ]);

        // Assert
        $this->assertDatabaseHas('davhars', [
            'id' => $davhar->id,
            'orc_id' => $orc->id,
            'number' => '4',
        ]);
    }

    /** @test */
    public function it_establishes_correct_relationships()
    {
        // Arrange
        $sukh = Sukh::factory()->create();
        $bair = Bair::factory()->create(['sukh_id' => $sukh->id]);
        $korpus = Korpus::factory()->create(['bair_id' => $bair->id]);
        $orc = Orc::factory()->create(['korpus_id' => $korpus->id]);

        $davhar = Davhar::create([
            'orc_id' => $orc->id,
            'number' => '4',
        ]);

        // Act & Assert
        $this->assertEquals($orc->id, $davhar->orc->id);
        $this->assertTrue($orc->davhars->contains($davhar));
        $this->assertEquals('4', $davhar->number);
    }

    /** @test */
    public function it_allows_multiple_davhars_in_same_orc()
    {
        // Arrange
        $sukh = Sukh::factory()->create();
        $bair = Bair::factory()->create(['sukh_id' => $sukh->id]);
        $korpus = Korpus::factory()->create(['bair_id' => $bair->id]);
        $orc = Orc::factory()->create(['korpus_id' => $korpus->id]);

        // Act - Create multiple davhars in same orc
        $davhar1 = Davhar::create([
            'orc_id' => $orc->id,
            'number' => '4',
        ]);

        $davhar2 = Davhar::create([
            'orc_id' => $orc->id,
            'number' => '5',
        ]);

        // Assert
        $this->assertDatabaseHas('davhars', ['id' => $davhar1->id, 'number' => '4']);
        $this->assertDatabaseHas('davhars', ['id' => $davhar2->id, 'number' => '5']);
        $this->assertEquals($davhar1->orc_id, $davhar2->orc_id);
    }

    /** @test */
    public function it_allows_davhars_in_different_orcs()
    {
        // Arrange
        $sukh = Sukh::factory()->create();
        $bair = Bair::factory()->create(['sukh_id' => $sukh->id]);
        $korpus = Korpus::factory()->create(['bair_id' => $bair->id]);
        $orc1 = Orc::factory()->create(['korpus_id' => $korpus->id, 'number' => '1']);
        $orc2 = Orc::factory()->create(['korpus_id' => $korpus->id, 'number' => '2']);

        // Act - Create davhars in different orcs
        $davhar1 = Davhar::create([
            'orc_id' => $orc1->id,
            'number' => '4',
        ]);

        $davhar2 = Davhar::create([
            'orc_id' => $orc2->id,
            'number' => '4',
        ]);

        // Assert
        $this->assertDatabaseHas('davhars', ['id' => $davhar1->id, 'number' => '4']);
        $this->assertDatabaseHas('davhars', ['id' => $davhar2->id, 'number' => '4']);
        $this->assertNotEquals($davhar1->orc_id, $davhar2->orc_id);
    }
}
