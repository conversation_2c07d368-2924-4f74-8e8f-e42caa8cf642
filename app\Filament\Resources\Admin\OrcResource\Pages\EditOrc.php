<?php

namespace App\Filament\Resources\Admin\OrcResource\Pages;

use App\Filament\Resources\Admin\OrcResource;
use App\Models\Orc;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditOrc extends EditRecord
{
    protected static string $resource = OrcResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    public function getBreadcrumbs(): array
    {
        $record = $this->getRecord();
        return [
            url()->route('filament.admin.resources.bairs.index') => 'Байрууд',
            url()->route('filament.admin.resources.bairs.edit', $record->korpus->bair_id) => $record->korpus->bair->name,
            url()->route('filament.admin.resources.korpuses.edit', $record->korpus_id) => "Блок: {$record->korpus->name}",
            '#' => "Орц: {$record->number}",
        ];
    }

    protected function getRedirectUrl(): string
    {
        $record = $this->getRecord();
        return url()->route('filament.admin.resources.korpuses.edit', $record->korpus_id);
    }
}
