<?php

namespace App\Filament\Resources\Admin\DavharResource\Pages;

use App\Filament\Resources\Admin\DavharResource;
use App\Models\Davhar;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditDavhar extends EditRecord
{
    protected static string $resource = DavharResource::class;

    public function getBreadcrumbs(): array
    {
        $record = $this->getRecord();
        return [
            url()->route('filament.admin.resources.bairs.index') => 'Байрууд',
            url()->route('filament.admin.resources.bairs.edit', $record->orc->korpus->bair_id) => $record->orc->korpus->bair->name,
            url()->route('filament.admin.resources.korpuses.edit', $record->orc->korpus_id) => "Блок: {$record->orc->korpus->name}",
            url()->route('filament.admin.resources.orcs.edit', $record->orc_id) => "Орц: {$record->orc->number}",
            '#' => "Давхар: {$record->number}",
        ];
    }

    protected function getRedirectUrl(): string
    {
        $record = $this->getRecord();
        return url()->route('filament.admin.resources.orcs.edit', $record->orc_id);
    }
}
